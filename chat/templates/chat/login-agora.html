<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Agora</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            width: 100vw;
            display: flex;
            overflow: hidden;
            background: #ffffff;
            margin: 0;
            padding: 0;
        }

        @keyframes colorChange {
            0% {
                background-color: #ED1C24;
            }
            20% {
                background-color: #39B54A;
            }
            40% {
                background-color: #27AAE1;
            }
            60% {
                background-color: #EE2A7B;
            }
            80% {
                background-color: #f7941d;
            }
            100% {
                background-color: #ED1C24;
            }
        }

        .main-container {
            width: 95vw;
            height: 95vh;
            margin: 2.5vh auto;
            background-color: white;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.1);
            border-radius: 50px;
            display: flex;
            overflow: hidden;
            max-width: 1400px;
            max-height: 900px;
        }

        .hero {
            flex: 2;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            min-height: 100%;
            overflow: hidden;
        }

        .hero-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-content img,
        .hero-content video {
            max-width: 90%;
            max-height: 90%;
            width: auto;
            height: auto;
            object-fit: contain;
            display: block;
        }

        .auth-form {
            flex: 1;
            background-color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            min-width: 400px;
            overflow: hidden;
        }

        .auth-form-content {
            width: 100%;
            max-width: 450px;
            text-align: center;
            border: 1px solid #ddd;
            padding: 3rem;
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .auth-form-content h2 {
            color: #333;
            margin-bottom: 2.5rem;
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 300;
        }

        .auth-form-content form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .auth-form-content input {
            padding: 18px 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            font-size: 18px;
            background-color: white;
            transition: border-color 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }

        .auth-form-content input:focus {
            outline: none;
            border-color: #f7941d;
        }

        .auth-form-content input::placeholder {
            color: #999;
            font-size: 18px;
        }

        .auth-form-content button {
            padding: 18px 32px;
            background-color: transparent;
            color: #b9b9b9;
            border: 1px solid #999;
            border-radius: 12px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .auth-form-content button[type="submit"] {
            background-color: #ED1C24;
            color: white;
            border-color: #ED1C24;
        }

        .auth-form-content button[type="submit"]:hover {
            color: white;
            background-color: #39B54A;
            border-color: #39B54A;
        }

        .auth-form-content button[type="button"] {
            background-color: #27AAE1;
            color: white;
            border-color: #27AAE1;
            transition: all 0.3s ease;
        }

        .auth-form-content button[type="button"]:hover {
            background-color: #EE2A7B;
            border-color: #EE2A7B;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(238, 42, 123, 0.3);
        }

        .auth-form button[type="button"] {
            margin-top: 2rem;
            padding: 18px 32px;
            border-radius: 14px;
            background-color: #39B54A;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            max-width: 450px;
        }

        .auth-form button[type="button"]:hover {
            background-color: #27AAE1;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(39, 170, 225, 0.3);
        }

        /* Form toggle styles */
        .form-container {
            width: 100%;
            max-width: 450px;
        }

        .login-form, .register-form {
            display: none;
        }

        .login-form.active, .register-form.active {
            display: block;
        }

        .back-to-login {
            background-color: transparent;
            color: #666;
            border: 1px solid #ddd;
            margin-top: 1rem;
        }

        .back-to-login:hover {
            background-color: #f5f5f5;
            color: #333;
            transform: none;
            box-shadow: none;
        }

        .register-btn {
            background-color: transparent;
            color: #666;
            border: 1px solid #ddd;
        }

        .register-btn:hover {
            background-color: #f5f5f5;
            color: #333;
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .main-container {
                width: 98vw;
                height: 98vh;
                margin: 1vh auto;
                flex-direction: column;
                border-radius: 20px;
            }
            
            .hero {
                flex: 1;
                min-height: 40vh;
                padding: 1rem;
            }
            
            .auth-form {
                flex: 1;
                min-height: 60vh;
                padding: 2rem;
                min-width: 350px;
            }

            .auth-form-content {
                padding: 2.5rem;
                margin-bottom: 1rem;
                max-width: 400px;
            }

            .hero-content img,
            .hero-content video {
                max-width: 95%;
                max-height: 95%;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
            }

            .auth-form-content {
                padding: 2rem;
                border: none;
                box-shadow: none;
                max-width: 350px;
            }

            .hero {
                padding: 0.5rem;
            }

            .auth-form {
                padding: 1.5rem;
                min-width: 300px;
            }
        }

        @media (max-height: 600px) {
            .main-container {
                height: 100vh;
                margin: 0 auto;
            }

            .hero {
                padding: 1rem;
            }

            .auth-form {
                padding: 1.5rem;
            }

            .auth-form-content {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <section class="hero">
            <div class="hero-content">
                <video autoplay muted loop style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain;">
                    <source src="Animation_Description_and_Video_Link.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </section>
        <section class="auth-form">
            <div class="form-container">
                <!-- Login Form -->
                <div class="login-form active auth-form-content" id="loginForm">
                    <img src="agora-name.svg" alt="Agora" style="max-width: 200px; margin-bottom: 2rem;">
                    <form action="">
                        <input type="text" placeholder="Username" required>
                        <input type="password" placeholder="Password" required>
                        <button type="submit">Iniciar Sesión</button>
                    </form>            
                </div>

                <!-- Register Form -->
                <div class="register-form auth-form-content" id="registerForm">
                    <img src="agora-name.svg" alt="Agora" style="max-width: 200px; margin-bottom: 2rem;">
                    <form action="">
                        <input type="text" placeholder="Username" required>
                        <input type="password" placeholder="Password" required>
                        <input type="password" placeholder="Confirm Password" required>
                        <button type="submit">HECHO</button>
                    </form>            
                </div>
            </div>
            
            <!-- Toggle Buttons -->
            <button type="button" id="registerBtn" class="register-btn">REGISTRATE</button>
            <button type="button" id="backToLoginBtn" class="back-to-login" style="display: none;">Volver al Login</button>
        </section>
    </div>

    <!-- Agora Background Animations -->
    <script src="agora-background-animations.js"></script>
    
    <script>
        // Form toggle functionality
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const registerBtn = document.getElementById('registerBtn');
        const backToLoginBtn = document.getElementById('backToLoginBtn');

        registerBtn.addEventListener('click', function() {
            loginForm.classList.remove('active');
            registerForm.classList.add('active');
            registerBtn.style.display = 'none';
            backToLoginBtn.style.display = 'block';
        });

        backToLoginBtn.addEventListener('click', function() {
            registerForm.classList.remove('active');
            loginForm.classList.add('active');
            backToLoginBtn.style.display = 'none';
            registerBtn.style.display = 'block';
        });
    </script>
</body>
</html>